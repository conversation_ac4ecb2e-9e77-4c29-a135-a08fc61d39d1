.translation-progress {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

/* 紧凑模式 - 用于文档列表中的翻译进度 */
.translation-progress.compact {
  background: transparent;
  border-radius: 4px;
  padding: 4px 8px;
  box-shadow: none;
  border: none;
  min-height: auto;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.translation-progress.compact .progress-header {
  margin-bottom: 4px;
  justify-content: center;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  flex-shrink: 0;
}

.status-icon.error {
  color: #dc3545;
}

.status-icon.success {
  color: #28a745;
}

.status-icon.processing {
  color: #007bff;
  animation: pulse 2s infinite;
}

.status-text {
  font-weight: 600;
  font-size: 14px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.translation-progress.compact .connection-status {
  display: none;
}

.connection-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.connection-indicator.connected {
  background-color: #28a745;
  box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
}

.connection-indicator.disconnected {
  background-color: #dc3545;
}

.connection-text {
  font-size: 12px;
  color: #6c757d;
}

.progress-content {
  margin-bottom: 12px;
}

.translation-progress.compact .progress-content {
  margin-bottom: 4px;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.translation-progress.compact .progress-bar-container {
  gap: 6px;
  margin-bottom: 4px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.translation-progress.compact .progress-bar {
  height: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  min-width: 40px;
  text-align: right;
}

.progress-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.translation-progress.compact .progress-details {
  gap: 2px;
}

.progress-message {
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

.translation-progress.compact .progress-message {
  font-size: 10px;
  text-align: center;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.translation-progress.compact .progress-stats {
  display: none;
}

.progress-count {
  font-weight: 500;
}

.progress-time {
  font-style: italic;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 8px;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .translation-progress {
    padding: 12px;
  }
  
  .progress-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .progress-stats {
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
  }
}
