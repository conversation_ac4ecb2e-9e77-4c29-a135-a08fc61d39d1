import React, { useState } from 'react';
import { Download, Eye, FileText, HardDrive, Clock, RefreshCw } from 'lucide-react';
import TranslationProgress from './TranslationProgress';
import { DocumentFile, DocumentRow } from '../types/document';
import { getDocumentFiles } from '../utils/preview';
import { isSupportedByOfficeViewer } from '../utils/officePreview';

interface DocumentListProps {
  documents: DocumentRow[];
  onPreview: (file: DocumentFile) => void;
  onDownload: (file: DocumentFile) => void;
  onRowPreview?: (rowid: string) => void; // 新增：行预览回调
  onSelectionChange?: (selectedDocuments: DocumentRow[]) => void; // 新增：选择变化回调
  onStartTranslation?: (rowId: string, sourceLanguage: string, targetLanguage: string) => void; // 新增：开始翻译回调
  onBatchTranslate?: () => void; // 新增：批量翻译回调
  onRefresh?: () => void; // 新增：刷新回调
  loading?: boolean;
}

const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  onPreview,
  onDownload,
  // onRowPreview, // 暂时禁用
  onSelectionChange,
  onStartTranslation,
  onBatchTranslate,
  onRefresh,
  loading = false
}) => {
  const [selectedDocs, setSelectedDocs] = useState<Set<string>>(new Set());

  // 检查文档是否已有翻译文件
  const hasTranslatedFiles = (doc: DocumentRow): boolean => {
    const { processedFiles } = getDocumentFiles(doc);
    return processedFiles.length > 0;
  };

  // 检查选中的文档中是否有已翻译的
  const hasTranslatedInSelection = (): boolean => {
    const selectedDocuments = documents.filter(doc => selectedDocs.has(doc._id));
    return selectedDocuments.some(hasTranslatedFiles);
  };

  // 解析文档文件数据
  // parseDocumentFiles 函数已移除，使用 getDocumentFiles 替代

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取翻译状态
  const getTranslationStatus = (doc: DocumentRow) => {
    // 根据明道云translations表的状态字段判断
    const status = doc['6886f7a4a849420e13f69b71']; // 翻译状态字段

    if (!status) {
      return 'not_started'; // 未开始
    }

    // 根据状态ID判断
    switch (status) {
      case '1784937f-c546-43f5-9d78-02b326a72bde':
        return 'pending'; // 待处理
      case 'd9a70a50-3369-4e62-96c8-4e183996368c':
        return 'translating'; // 翻译中
      case '7c60c3c6-c56e-4ab8-8979-278f1b359e80':
        return 'completed'; // 已完成
      case '80fa2f81-3381-4228-b0a6-0ec3517fe205':
        return 'failed'; // 失败
      case '99b06107-282a-437e-8625-7a60c921cf3f':
        return 'cancelled'; // 已取消
      default:
        return 'not_started';
    }
  };

  // 获取翻译状态显示文本和样式
  const getTranslationStatusDisplay = (status: string) => {
    switch (status) {
      case 'not_started':
        return { text: '未开始', className: 'status-not-started', icon: <Clock size={14} /> };
      case 'pending':
        return { text: '待处理', className: 'status-pending', icon: <Clock size={14} /> };
      case 'translating':
        return { text: '翻译中', className: 'status-translating', icon: <div className="spinner-small"></div> };
      case 'completed':
        return { text: '已完成', className: 'status-completed', icon: <FileText size={14} /> };
      case 'failed':
        return { text: '失败', className: 'status-failed', icon: <FileText size={14} /> };
      case 'cancelled':
        return { text: '已取消', className: 'status-cancelled', icon: <FileText size={14} /> };
      default:
        return { text: '未知', className: 'status-unknown', icon: <Clock size={14} /> };
    }
  };



  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="text-red-500" size={20} />;
      case 'doc':
      case 'docx':
        return <FileText className="text-blue-500" size={20} />;
      case 'xls':
      case 'xlsx':
        return <FileText className="text-green-500" size={20} />;
      case 'ppt':
      case 'pptx':
        return <FileText className="text-orange-500" size={20} />;
      default:
        return <FileText className="text-gray-500" size={20} />;
    }
  };

  const toggleDocSelection = (docId: string) => {
    const newSelected = new Set(selectedDocs);
    if (newSelected.has(docId)) {
      newSelected.delete(docId);
    } else {
      newSelected.add(docId);
    }
    setSelectedDocs(newSelected);

    // 通知父组件选择变化
    if (onSelectionChange) {
      const selectedDocuments = documents.filter(doc => newSelected.has(doc._id));
      onSelectionChange(selectedDocuments);
    }
  };

  const selectAllDocs = () => {
    let newSelected: Set<string>;
    if (selectedDocs.size === documents.length) {
      newSelected = new Set();
    } else {
      newSelected = new Set(documents.map(doc => doc._id));
    }
    setSelectedDocs(newSelected);

    // 通知父组件选择变化
    if (onSelectionChange) {
      const selectedDocuments = documents.filter(doc => newSelected.has(doc._id));
      onSelectionChange(selectedDocuments);
    }
  };

  if (loading) {
    return (
      <div className="document-list loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="document-list empty">
        <div className="empty-state">
          <FileText size={64} className="empty-icon" />
          <h3>暂无文档</h3>
          <p>请上传文档或检查筛选条件</p>
        </div>
      </div>
    );
  }

  return (
    <div className="document-list">
      <div className="list-header">
        <div className="list-controls">
          <label className="checkbox-container">
            <input
              type="checkbox"
              checked={selectedDocs.size === documents.length && documents.length > 0}
              onChange={selectAllDocs}
            />
            <span className="checkmark"></span>
            全选 ({selectedDocs.size}/{documents.length})
          </label>

          {onRefresh && (
            <button
              className="btn btn-outline btn-xs refresh-btn"
              onClick={onRefresh}
              disabled={loading}
              title="刷新文档列表"
            >
              <RefreshCw size={12} />
              刷新
            </button>
          )}

          {selectedDocs.size > 0 && onBatchTranslate && (
            <button
              className={`btn btn-xs batch-translate-btn ${hasTranslatedInSelection() ? 'btn-disabled' : 'btn-primary'}`}
              onClick={hasTranslatedInSelection() ? undefined : onBatchTranslate}
              disabled={hasTranslatedInSelection()}
              title={hasTranslatedInSelection() ? '您选择的文件中存在已翻译文件' : `批量翻译 ${selectedDocs.size} 个文档`}
            >
              批量翻译 ({selectedDocs.size})
            </button>
          )}
        </div>
        <div className="list-stats">
          共 {documents.length} 个文档
        </div>
      </div>

      <div className="document-items">
        {documents.map((doc) => {
          const isSelected = selectedDocs.has(doc._id);
          const documentFiles = getDocumentFiles(doc);
          const { originalFiles, processedFiles } = documentFiles;
          const translationStatus = getTranslationStatus(doc);
          const statusDisplay = getTranslationStatusDisplay(translationStatus);

          return (
            <div key={doc._id} className={`document-item ${isSelected ? 'selected' : ''}`}>
              {/* 左右分栏显示文件，复选框在最左边 */}
              <div className="document-files-container">
                {/* 最左边的复选框 */}
                <div className="document-checkbox">
                  <label className="checkbox-container">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => toggleDocSelection(doc._id)}
                    />
                    <span className="checkmark"></span>
                  </label>
                </div>
                {/* 左侧：原始文件 */}
                <div className="original-files-section">
                  <h5 className="file-section-title">原文档</h5>
                  {originalFiles.length > 0 ? (
                    originalFiles.map((file, index) => (
                      <div key={`original-${file.file_id}-${index}`} className="file-card">
                        <div className="file-info">
                          <div className="file-icon">
                            {getFileTypeIcon(file.original_file_name)}
                          </div>
                          <div className="file-details">
                            <h4 className="file-name" title={file.original_file_name}>
                              {file.original_file_name}
                            </h4>
                            <div className="file-meta">
                              <span className="file-size">
                                <HardDrive size={12} />
                                {formatFileSize(file.file_size)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="file-actions">
                          {/* Office Online 预览 */}
                          {file.allow_view && isSupportedByOfficeViewer(file.original_file_name) && (
                            <button
                              className="btn btn-success action-btn"
                              onClick={() => onPreview(file)}
                              title="预览原文档"
                            >
                              <Eye size={16} />
                              预览
                            </button>
                          )}
                          {file.allow_down && (
                            <button
                              className="btn btn-primary action-btn"
                              onClick={() => onDownload(file)}
                              title="下载原文档"
                            >
                              <Download size={16} />
                              下载
                            </button>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="no-files-message">
                      <FileText size={32} className="no-files-icon" />
                      <p>暂无原文档</p>
                    </div>
                  )}
                </div>

                {/* 右侧：翻译后文件 */}
                <div className="translated-files-section">
                  <h5 className="file-section-title">翻译文档</h5>
                  {processedFiles.length > 0 ? (
                    processedFiles.map((file, index) => (
                      <div key={`processed-${file.file_id}-${index}`} className="file-card processed">
                        <div className="file-info">
                          <div className="file-icon">
                            {getFileTypeIcon(file.original_file_name)}
                          </div>
                          <div className="file-details">
                            <h4 className="file-name" title={file.original_file_name}>
                              {file.original_file_name}
                            </h4>
                            <div className="file-meta">
                              <span className="file-size">
                                <HardDrive size={12} />
                                {formatFileSize(file.file_size)}
                              </span>
                              <span className="file-status">已翻译</span>
                            </div>
                          </div>
                        </div>

                        <div className="file-actions">
                          {/* Office Online 预览翻译文件 */}
                          {isSupportedByOfficeViewer(file.original_file_name) && (
                            <button
                              className="btn btn-success action-btn"
                              onClick={() => onPreview(file)}
                              title="预览翻译文档"
                            >
                              <Eye size={16} />
                              预览
                            </button>
                          )}
                          {file.allow_down && (
                            <button
                              className="btn btn-primary action-btn"
                              onClick={() => onDownload(file)}
                              title="下载翻译文档"
                            >
                              <Download size={16} />
                              下载
                            </button>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="no-files-message">
                      {translationStatus === 'not_started' ? (
                        <div className="start-translation-area">
                          {onStartTranslation && (
                            <button
                              className="btn btn-primary start-translation-btn"
                              onClick={() => onStartTranslation(doc.rowid, 'zh', 'en')}
                              title="开始翻译"
                            >
                              开始翻译
                            </button>
                          )}
                        </div>
                      ) : translationStatus === 'translating' || translationStatus === 'pending' ? (
                        <div className="translating-area">
                          <TranslationProgress[Symbol]......
                            translationId={doc.rowid}
                            onComplete={() => {
                              // 翻译完成后刷新文档列表
                              window.location.reload();
                            }}
                            onError={(error) => {
                              console.error('翻译错误:', error);
                            }}
                          />
                        </div>
                      ) : (
                        <div className="no-translation-area">
                          <FileText size={32} className="no-files-icon" />
                          <p>暂无翻译文档</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DocumentList;
