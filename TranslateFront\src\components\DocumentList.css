.document-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

.document-list.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.document-list.empty {
  justify-content: center;
  align-items: center;
}

.empty-state {
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 4px 4px 0 0;
  min-height: 36px;
}

.list-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 12px;
  user-select: none;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 14px;
  height: 14px;
  border: 1px solid #dee2e6;
  border-radius: 2px;
  position: relative;
  transition: all 0.1s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background-color: #007bff;
  border-color: #007bff;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 3px;
  height: 6px;
  border: solid white;
  border-width: 0 1px 1px 0;
  transform: rotate(45deg);
}

.list-stats {
  font-size: 14px;
  color: #6c757d;
}

.document-items {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  padding: 16px;
}

.document-item {
  margin-bottom: 2px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background-color: white;
  transition: all 0.1s ease;
}

.document-item:hover {
  border-color: #007bff;
  box-shadow: 0 1px 3px rgba(0, 123, 255, 0.1);
  background-color: #f8f9ff;
}

.document-item.selected {
  border-color: #007bff;
  background-color: #e3f2fd;
}

/* 文档头部样式 - 已删除，不再需要 */



.document-files-container {
  display: flex;
  gap: 8px;
  padding: 8px;
  min-height: 60px;
  align-items: stretch;
}

.document-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  flex-shrink: 0;
}

.original-files-section,
.translated-files-section {
  flex: 1;
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 3px;
  padding: 6px;
}

.original-files-section {
  border-left: 3px solid #007bff;
}

.translated-files-section {
  border-left: 3px solid #28a745;
  background-color: #f8fff9;
}

.file-section-title {
  margin: 0 0 6px 0;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  padding-bottom: 4px;
  border-bottom: 1px solid #e9ecef;
}

.file-card.processed {
  border-left: 4px solid #28a745;
  background-color: #f8fff9;
}

.file-status {
  color: #28a745;
  font-size: 9px;
  font-weight: 500;
  background-color: #d4edda;
  padding: 1px 4px;
  border-radius: 2px;
  line-height: 1;
}

/* 无文件状态 */
.no-files-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  text-align: center;
  color: #6c757d;
  min-height: 36px;
  gap: 8px;
}

.no-files-icon {
  opacity: 0.5;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.no-files-message p {
  margin: 0;
  font-size: 11px;
  line-height: 1.2;
  flex: 1;
}

.no-files-message.translating {
  color: #856404;
}

.translating-animation {
  flex-shrink: 0;
}

.translating-animation .spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ffc107;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.file-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 6px;
  background-color: white;
  border-radius: 3px;
  border: 1px solid #e9ecef;
  margin-bottom: 2px;
  transition: all 0.1s ease;
  min-height: 36px;
}

.file-card:hover {
  border-color: #007bff;
  box-shadow: 0 1px 2px rgba(0, 123, 255, 0.1);
  background-color: #f8f9ff;
}

.file-card:last-child {
  margin-bottom: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.file-meta {
  display: flex;
  gap: 8px;
  font-size: 10px;
  color: #6c757d;
  margin-top: 2px;
}

.file-size {
  display: flex;
  align-items: center;
  gap: 4px;
}

.file-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.action-btn {
  padding: 3px 8px;
  font-size: 10px;
  min-width: auto;
  height: 24px;
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-files-container {
    flex-direction: column;
    gap: 8px;
  }

  .document-checkbox {
    width: 100%;
    justify-content: flex-start;
    padding: 4px 0;
  }

  .file-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .no-files-message {
    padding: 8px;
    min-height: 32px;
    flex-direction: column;
    gap: 4px;
  }
}

/* 开始翻译区域样式 */
.start-translation-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
}

.start-translation-area .no-files-icon {
  width: 24px;
  height: 24px;
  opacity: 0.6;
}

.start-translation-area p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.start-translation-btn {
  padding: 6px 12px;
  font-size: 11px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.start-translation-btn:hover {
  background-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* 翻译中区域样式 */
.translating-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  color: #856404;
}

.translating-area p {
  margin: 0;
  font-size: 12px;
}

/* 无翻译区域样式 */
.no-translation-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
}

.no-translation-area .no-files-icon {
  width: 24px;
  height: 24px;
  opacity: 0.6;
}

.no-translation-area p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}
