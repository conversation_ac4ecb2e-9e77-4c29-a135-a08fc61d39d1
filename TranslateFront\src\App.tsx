import { useState, useEffect } from 'react';
import { Download, RefreshCw, Search, User, LogOut, Settings, CreditCard } from 'lucide-react';
import FileUpload from './components/FileUpload';
import DocumentList from './components/DocumentList';
import LoginPage from './components/LoginPage';
import TranslationSettings from './components/TranslationSettings';
// import DocumentPreview from './components/DocumentPreview';
// import OnlyOfficePreview from './components/OnlyOfficePreview';
// import OnlyOfficeTest from './components/OnlyOfficeTest';
import StatusMessage from './components/StatusMessage';
import BatchDownloadModal from './components/BatchDownloadModal';
import Pagination from './components/Pagination';
import LoginModal from './components/LoginModal';
import RegisterModal from './components/RegisterModal';
import RechargeModal from './components/RechargeModal';
import TranslationProgress from './components/TranslationProgress';
import BatchTranslateModal from './components/BatchTranslateModal';
// import ApiTest from './components/ApiTest';
// import PreviewTest from './components/PreviewTest';
// import DataStructureInfo from './components/DataStructureInfo';
// import SystemStatus from './components/SystemStatus';
import { DocumentFile, DocumentRow, UploadFile, UserAccount } from './types/document';
import { TranslationSettings as TranslationSettingsType } from './types/translation';
import { downloadFile } from './utils/download';
import { getDocuments, uploadMultipleFiles, getDocumentsTotalNum, startTranslation } from './services/api';
import { authService } from './services/auth';
import { useMessages } from './hooks/useMessages';
import { openPreviewWindow, getDocumentFiles } from './utils/preview';
import { getFilePreviewUrl } from './utils/officePreview';
import './App.css';
import './components/FileUpload.css';
import './components/DocumentList.css';
import './components/DocumentPreview.css';
import './components/BatchDownloadModal.css';
import './components/LoginPage.css';
// import './components/OnlyOfficePreview.css';
import './components/StatusMessage.css';
// import './components/ApiTest.css';
// import './components/PreviewTest.css';
// import './components/DataStructureInfo.css';
// import './components/SystemStatus.css';

// 模拟数据已移除，现在使用真实API数据

function App() {
  const [documents, setDocuments] = useState<DocumentRow[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 用户认证相关状态
  const [currentUser, setCurrentUser] = useState<UserAccount | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [showRechargeModal, setShowRechargeModal] = useState(false);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  // const [previewFile, setPreviewFile] = useState<DocumentFile | null>(null);
  // const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  console.log('Upload progress:', uploadProgress); // 避免未使用警告
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewFileName, setPreviewFileName] = useState<string>('');
  const [activeTranslations, setActiveTranslations] = useState<Set<number>>(new Set());
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set());
  const [showBatchTranslateModal, setShowBatchTranslateModal] = useState(false);

  // OnlyOffice 预览状态（暂时禁用）
  // const [onlyOfficePreview, setOnlyOfficePreview] = useState<{
  //   isOpen: boolean;
  //   rowid: string;
  //   fileName: string;
  // }>({
  //   isOpen: false,
  //   rowid: '',
  //   fileName: ''
  // });

  // 测试页面状态（暂时禁用）
  // const [showTestPage, setShowTestPage] = useState<boolean>(false);

  // 批量下载模态框状态
  const [batchDownloadModal, setBatchDownloadModal] = useState<{
    isOpen: boolean;
    selectedDocuments: DocumentRow[];
  }>({
    isOpen: false,
    selectedDocuments: []
  });

  // 翻译设置相关状态
  const [showTranslationSettings, setShowTranslationSettings] = useState(false);
  const [currentTranslationSettings, setCurrentTranslationSettings] = useState<TranslationSettingsType | null>(null);

  // 消息系统
  const { messages, removeMessage, showSuccess, showError, showInfo, showWarning } = useMessages();

  // 加载文档总数
  const loadTotalCount = async () => {
    console.log('🔢 开始获取文档总数...');
    try {
      const total = await getDocumentsTotalNum();
      console.log('🔢 获取到总数:', total);
      setTotalItems(total);
      setTotalPages(Math.ceil(total / pageSize));
      console.log(`🔢 设置状态 - 总记录数: ${total}, 总页数: ${Math.ceil(total / pageSize)}`);
    } catch (error) {
      console.error('🔢 获取总数失败:', error);
      setTotalItems(0);
      setTotalPages(0);
    }
  };

  // 加载文档数据
  const loadDocuments = async (page: number = currentPage, size: number = pageSize) => {
    setLoading(true);
    try {
      const response = await getDocuments({
        pageSize: size,
        pageIndex: page,
        viewId: '',
        listType: 0
      });

      if (response.success && response.data && response.data.rows) {
        setDocuments(response.data.rows);
        showInfo(`成功加载第 ${page} 页，共 ${response.data.rows.length} 条记录`);
      } else {
        console.error('API 返回错误:', response.message);
        showError('获取文档列表失败');
        setDocuments([]);
      }
    } catch (error) {
      console.error('加载文档失败:', error);
      showError('网络连接失败');
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadDocuments(page, pageSize);
  };

  // 处理每页大小变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    setTotalPages(Math.ceil(totalItems / newPageSize));
    loadDocuments(1, newPageSize);
  };

  // 用户认证相关函数
  const checkAuthStatus = () => {
    const user = authService.getCurrentUser();
    const authenticated = authService.isAuthenticated();
    const token = authService.getToken();

    setCurrentUser(user);
    setIsAuthenticated(authenticated);

    console.log('用户认证状态检查:', {
      user,
      authenticated,
      token: token ? '已设置' : '未设置',
      localStorage_current_user: localStorage.getItem('current_user'),
      localStorage_auth_token: localStorage.getItem('auth_token')
    });
  };

  const handleLoginSuccess = (user: UserAccount) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    setShowLoginModal(false);
    showSuccess(`欢迎回来，${user.fullName || user.username}！`);
  };

  const handleRegisterSuccess = (user: UserAccount) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    setShowRegisterModal(false);
    showSuccess(`注册成功，欢迎 ${user.fullName || user.username}！`);
  };

  const handleLogout = () => {
    authService.logout();
    setCurrentUser(null);
    setIsAuthenticated(false);
    showInfo('已退出登录');
  };

  const handleRechargeSuccess = () => {
    // 刷新用户信息
    checkAuthStatus();
    setShowRechargeModal(false);
    showSuccess('充值成功！');
  };

  // 翻译设置相关处理函数
  const handleShowTranslationSettings = () => {
    setShowTranslationSettings(true);
  };

  const handleCloseTranslationSettings = () => {
    setShowTranslationSettings(false);
  };

  const handleTranslationSettingsChange = (settings: TranslationSettingsType) => {
    setCurrentTranslationSettings(settings);
    showSuccess('翻译格式设置已更新！');
  };

  // 转换翻译设置格式（前端格式 -> 后端格式）
  const convertTranslationSettings = (settings: TranslationSettingsType | null) => {
    if (!settings) return null;

    return {
      paragraph: {
        font_family: settings.paragraph.fontFamily,
        font_size: settings.paragraph.fontSize,
        bold: settings.paragraph.bold,
        italic: settings.paragraph.italic,
        underline: settings.paragraph.underline,
        text_align: settings.paragraph.textAlign,
        text_indent: settings.paragraph.textIndent,
        line_height: settings.paragraph.lineHeight,
        margin_top: settings.paragraph.marginTop,
        color: settings.paragraph.color,
        background_color: settings.paragraph.backgroundColor
      },
      table: {
        font_family: settings.table.fontFamily,
        font_size: settings.table.fontSize,
        bold: settings.table.bold,
        italic: settings.table.italic,
        underline: settings.table.underline,
        text_align: settings.table.textAlign,
        text_indent: settings.table.textIndent,
        line_height: settings.table.lineHeight,
        margin_top: settings.table.marginTop,
        color: settings.table.color,
        background_color: settings.table.backgroundColor
      },
      header: {
        font_family: settings.header.fontFamily,
        font_size: settings.header.fontSize,
        bold: settings.header.bold,
        italic: settings.header.italic,
        underline: settings.header.underline,
        text_align: settings.header.textAlign,
        text_indent: settings.header.textIndent,
        line_height: settings.header.lineHeight,
        margin_top: settings.header.marginTop,
        color: settings.header.color,
        background_color: settings.header.backgroundColor
      },
      enable_paragraph: settings.enableParagraph,
      enable_table: settings.enableTable,
      enable_header: settings.enableHeader
    };
  };

  // 开始翻译处理函数
  const handleStartTranslation = async (rowId: string, sourceLanguage: string, targetLanguage: string) => {
    try {
      showInfo('正在开始翻译...');

      // 转换并传递当前的翻译设置
      const backendSettings = convertTranslationSettings(currentTranslationSettings);
      const result = await startTranslation(rowId, sourceLanguage, targetLanguage, backendSettings);

      if (result.success) {
        showSuccess(result.message || '翻译已开始！');

        // 添加到活跃翻译列表（假设result包含翻译ID）
        if (result.translationId) {
          setActiveTranslations(prev => new Set([...prev, result.translationId]));
        }

        // 刷新文档列表以更新状态
        await loadDocuments(currentPage, pageSize);
      } else {
        showError(result.message || '开始翻译失败');
      }
    } catch (error) {
      console.error('开始翻译失败:', error);
      showError('开始翻译失败，请重试');
    }
  };

  // 翻译完成回调
  const handleTranslationComplete = (translationId: number) => {
    setActiveTranslations(prev => {
      const newSet = new Set(prev);
      newSet.delete(translationId);
      return newSet;
    });
    showSuccess('翻译完成！');
    loadDocuments(currentPage, pageSize);
  };

  // 文档选择处理
  const handleSelectionChange = (selectedDocuments: DocumentRow[]) => {
    const selectedIds = new Set(selectedDocuments.map(doc => doc._id));
    setSelectedDocuments(selectedIds);
  };

  // 检查文档是否已有翻译文件
  const hasTranslatedFiles = (doc: DocumentRow): boolean => {
    // 使用getDocumentFiles函数获取文档文件
    const { processedFiles } = getDocumentFiles(doc);
    return processedFiles.length > 0;
  };

  // 批量翻译处理
  const handleBatchTranslate = () => {
    if (selectedDocuments.size === 0) {
      showWarning('请先选择要翻译的文档');
      return;
    }

    // 检查选中的文档中是否有已翻译的
    const selectedDocs = documents.filter(doc => selectedDocuments.has(doc._id));
    const alreadyTranslated = selectedDocs.filter(hasTranslatedFiles);

    if (alreadyTranslated.length > 0) {
      showWarning(`选中的文档中有 ${alreadyTranslated.length} 个已有翻译文件，请取消选择后重试`);
      return;
    }

    setShowBatchTranslateModal(true);
  };

  // 确认批量翻译
  const confirmBatchTranslate = async () => {
    try {
      setShowBatchTranslateModal(false);
      showInfo(`开始批量翻译 ${selectedDocuments.size} 个文档...`);

      const selectedDocs = documents.filter(doc => selectedDocuments.has(doc._id));
      let successCount = 0;
      let failCount = 0;

      for (const doc of selectedDocs) {
        try {
          const backendSettings = convertTranslationSettings(currentTranslationSettings);
          const result = await startTranslation(doc.rowid, 'zh', 'en', backendSettings);

          if (result.success) {
            successCount++;
            if (result.translationId) {
              setActiveTranslations(prev => new Set([...prev, result.translationId]));
            }
          } else {
            failCount++;
          }
        } catch (error) {
          console.error(`翻译文档 ${doc.rowid} 失败:`, error);
          failCount++;
        }
      }

      if (successCount > 0) {
        showSuccess(`成功开始翻译 ${successCount} 个文档`);
      }
      if (failCount > 0) {
        showError(`${failCount} 个文档翻译失败`);
      }

      // 清空选择并刷新列表
      setSelectedDocuments(new Set());
      await loadDocuments(currentPage, pageSize);

    } catch (error) {
      console.error('批量翻译失败:', error);
      showError('批量翻译失败，请重试');
    }
  };

  useEffect(() => {
    console.log('🚀 useEffect 开始执行...');
    // 检查用户认证状态
    checkAuthStatus();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 当用户登录状态改变时，加载文档数据
  useEffect(() => {
    if (isAuthenticated) {
      console.log('🚀 用户已登录，开始初始化数据...');
      const initData = async () => {
        try {
          console.log('🚀 步骤1: 获取总数');
          await loadTotalCount();
          console.log('🚀 步骤2: 加载文档');
          await loadDocuments(1, 100); // 明确指定参数
          console.log('🚀 初始化完成');
        } catch (error) {
          console.error('🚀 初始化失败:', error);
        }
      };
      initData();
    } else {
      console.log('🚀 用户未登录，清空文档数据');
      // 用户未登录时清空文档数据
      setDocuments([]);
      setTotalItems(0);
      setTotalPages(0);
      setCurrentPage(1);
    }
  }, [isAuthenticated]); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理文件上传
  const handleFilesSelected = (files: UploadFile[]) => {
    console.log('选择的文件:', files);
  };

  const handleUpload = async (files: UploadFile[], sourceLanguage: string, targetLanguage: string) => {
    console.log('开始上传文件:', files, { sourceLanguage, targetLanguage });

    try {
      // 过滤出状态为 pending 的文件
      const filesToUpload = files.filter(f => f.status === 'pending').map(f => f.file);

      if (filesToUpload.length === 0) {
        showError('没有可上传的文件');
        return;
      }

      // 使用新的仅上传文件 API
      const results = await uploadMultipleFiles(
        filesToUpload,
        sourceLanguage,
        targetLanguage,
        (progress, fileName) => {
          console.log(`上传进度: ${progress}% - ${fileName}`);
          setUploadProgress(prev => ({
            ...prev,
            [fileName]: progress
          }));
        }
      );

      // 检查上传结果
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        showSuccess(`成功上传 ${successCount} 个文件${failCount > 0 ? `，失败 ${failCount} 个` : ''}，可在列表中手动开始翻译`);
        // 上传完成后重新加载文档列表
        await loadDocuments();
      } else {
        showError('所有文件上传失败，请检查网络连接或文件格式');
      }

      // 清空上传进度
      setUploadProgress({});
    } catch (error) {
      console.error('上传失败:', error);
      showError('上传失败，请稍后重试');
      setUploadProgress({});
    }
  };

  // 处理文档预览（已移除，使用内嵌预览）
  // const handlePreview = (file: DocumentFile) => {
  //   setPreviewFile(file);
  //   setIsPreviewOpen(true);
  // };

  // 处理行预览（翻译后文档）- 暂时禁用 OnlyOffice
  const handleRowPreview = (rowid: string) => {
    showInfo('正在打开翻译后的文档预览...');
    openPreviewWindow(rowid);
  };

  // 关闭 OnlyOffice 预览（暂时禁用）
  // const closeOnlyOfficePreview = () => {
  //   setOnlyOfficePreview({
  //     isOpen: false,
  //     rowid: '',
  //     fileName: ''
  //   });
  // };

  // 处理文档下载
  const handleDownload = async (file: DocumentFile) => {
    try {
      await downloadFile(file);
      showSuccess(`开始下载文件: ${file.original_file_name}`);
    } catch (error) {
      console.error('下载失败:', error);
      showError('下载失败，请稍后重试');
    }
  };

  // 处理内嵌预览
  const handleEmbeddedPreview = async (file: DocumentFile) => {
    const url = getFilePreviewUrl(file);

    if (url) {
      setPreviewUrl(url);
      setPreviewFileName(file.original_file_name);
    } else {
      showError('该文件格式不支持在线预览');
    }
  };

  // 关闭预览
  const closePreview = () => {
    setPreviewUrl(null);
    setPreviewFileName('');
  };

  // 批量下载 - 打开选择模态框
  const handleBatchDownload = async () => {
    if (selectedDocuments.size === 0) {
      showError('请先选择要下载的文档');
      return;
    }

    const selectedDocs = documents.filter(doc => selectedDocuments.has(doc._id));
    setBatchDownloadModal({
      isOpen: true,
      selectedDocuments: selectedDocs
    });
  };

  // 确认批量下载
  const confirmBatchDownload = (downloadType: 'original' | 'translated' | 'both') => {
    const { selectedDocuments } = batchDownloadModal;

    showInfo(`开始批量下载 ${selectedDocuments.length} 个文档...`);

    selectedDocuments.forEach((doc, index) => {
      setTimeout(() => {
        downloadDocumentByType(doc, downloadType);

        if (index === selectedDocuments.length - 1) {
          showSuccess('批量下载已开始');
        }
      }, index * 500); // 延迟下载，避免浏览器阻止
    });
  };

  // 根据类型下载文档
  const downloadDocumentByType = (doc: DocumentRow, downloadType: 'original' | 'translated' | 'both') => {
    const documentFiles = getDocumentFiles(doc);

    if (downloadType === 'original' || downloadType === 'both') {
      // 下载原文档
      documentFiles.originalFiles.forEach((file, fileIndex) => {
        setTimeout(() => {
          const link = document.createElement('a');
          link.href = file.original_file_full_path;
          link.download = file.original_file_name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }, fileIndex * 200);
      });
    }

    if (downloadType === 'translated' || downloadType === 'both') {
      // 下载翻译文档
      documentFiles.processedFiles.forEach((file, fileIndex) => {
        setTimeout(() => {
          const link = document.createElement('a');
          link.href = file.original_file_full_path;
          link.download = file.original_file_name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }, fileIndex * 200 + (downloadType === 'both' ? 1000 : 0));
      });
    }
  };

  // 关闭批量下载模态框
  const closeBatchDownloadModal = () => {
    setBatchDownloadModal({
      isOpen: false,
      selectedDocuments: []
    });
  };





  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();

    // 搜索用户名
    if (doc.ownerid.fullname.toLowerCase().includes(searchLower)) {
      return true;
    }

    // 搜索文档中的文件名（包括原始文件和翻译后文件）
    return Object.keys(doc).some(key => {
      if (typeof doc[key] === 'string' && doc[key].startsWith('[{')) {
        try {
          const files = JSON.parse(doc[key]);
          if (Array.isArray(files)) {
            return files.some((file: DocumentFile) =>
              file.original_file_name.toLowerCase().includes(searchLower)
            );
          }
        } catch (error) {
          return false;
        }
      }
      return false;
    });
  });

  // 如果用户未登录，显示登录页面
  if (!isAuthenticated) {
    return (
      <div className="app">
        {/* 消息提示 */}
        <div className="status-messages-container">
          {messages.map((message) => (
            <StatusMessage
              key={message.id}
              type={message.type}
              message={message.message}
              onClose={() => removeMessage(message.id)}
            />
          ))}
        </div>

        <LoginPage
          onLoginSuccess={handleLoginSuccess}
          onRegisterSuccess={handleRegisterSuccess}
        />
      </div>
    );
  }

  // 用户已登录，显示主应用界面
  return (
    <div className="app">
      {/* 消息提示 */}
      <div className="status-messages-container">
        {messages.map((message) => (
          <StatusMessage
            key={message.id}
            type={message.type}
            message={message.message}
            onClose={() => removeMessage(message.id)}
          />
        ))}
      </div>

      {/* 头部 */}
      <div className="app-header">
        <div className="header-content">
          <div className="logo-section">
            <h1>SMILE TRANS</h1>
            <p>智能文档翻译系统</p>
          </div>
          {isAuthenticated && currentUser && (
            <div className="user-header-info">
              <div className="user-avatar-small">
                <User size={20} />
              </div>
              <div className="user-details-small">
                <span className="user-name-small">{currentUser.fullName || currentUser.username}</span>
                <span className="user-quota-small">{(currentUser.totalQuota - currentUser.usedQuota).toLocaleString()}字符</span>
              </div>
              <button
                className="btn btn-outline btn-sm"
                onClick={handleLogout}
              >
                <LogOut size={16} />
                退出
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 主内容区域 - 侧边栏布局 */}
      <div className="main-container">
        {/* 左侧边栏 */}
        <div className="sidebar">
          {/* 用户中心卡片 */}
          {isAuthenticated && currentUser && (
            <div className="sidebar-card user-card">
              <div className="card-header">
                <User size={20} />
                <h3>用户中心</h3>
              </div>
              <div className="user-info">
                <div className="user-avatar">
                  <User size={32} />
                </div>
                <div className="user-details">
                  <div className="user-name">{currentUser.fullName || currentUser.username}</div>
                  <div className="user-quota">
                    剩余字符: {(currentUser.totalQuota - currentUser.usedQuota).toLocaleString()}
                  </div>
                </div>
              </div>
              <div className="user-actions">
                <button
                  className="btn btn-outline btn-sm"
                  onClick={() => setShowRechargeModal(true)}
                >
                  <CreditCard size={16} />
                  充值
                </button>
                <button
                  className="btn btn-outline btn-sm"
                  onClick={handleLogout}
                >
                  <LogOut size={16} />
                  退出
                </button>
              </div>
            </div>
          )}

          {/* 翻译设置卡片 */}
          <div className="sidebar-card settings-card">
            <div className="card-header">
              <Settings size={20} />
              <h3>翻译设置</h3>
            </div>
            <div className="card-content">
              <button
                className="btn btn-primary btn-full"
                onClick={handleShowTranslationSettings}
              >
                配置翻译格式
              </button>
            </div>
          </div>

          {/* 文件上传卡片 */}
          <div className="sidebar-card upload-card">
            <div className="card-header">
              <Download size={20} />
              <h3>文件上传</h3>
            </div>
            <div className="card-content">
              <FileUpload
                onFilesSelected={handleFilesSelected}
                onUpload={handleUpload}
                maxFileSize={50}
                multiple={true}
              />
            </div>
          </div>

          {/* 筛选卡片 */}
          <div className="sidebar-card filter-card">
            <div className="card-header">
              <Search size={20} />
              <h3>筛选</h3>
            </div>
            <div className="card-content">
              <div className="search-box">
                <input
                  type="text"
                  placeholder="搜索文档名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
              <div className="filter-actions">
                <button
                  className="btn btn-outline btn-sm"
                  onClick={() => {
                    loadTotalCount();
                    loadDocuments(currentPage, pageSize);
                  }}
                  disabled={loading}
                >
                  <RefreshCw size={16} />
                  刷新
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧主内容区域 */}
        <div className="main-content">
          {/* 活跃翻译进度 */}
          {activeTranslations.size > 0 && (
            <div className="active-translations">
              <h4>翻译进度</h4>
              {Array.from(activeTranslations).map(translationId => (
                <TranslationProgress
                  key={translationId}
                  translationId={translationId}
                  onComplete={() => handleTranslationComplete(translationId)}
                  onError={(error) => showError(`翻译失败: ${error}`)}
                />
              ))}
            </div>
          )}

          {/* 文档列表区域 */}
          <div className="documents-section">
            {previewUrl ? (
              // 预览界面
              <div className="preview-container">
                <div className="preview-header">
                  <button
                    className="btn btn-secondary back-btn"
                    onClick={closePreview}
                  >
                    ← 返回列表
                  </button>
                  <h3 className="preview-title">{previewFileName}</h3>
                </div>
                <div className="preview-frame-container">
                  <iframe
                    src={previewUrl}
                    className="preview-frame"
                    title={`预览 ${previewFileName}`}
                    style={{ border: 'none' }}
                    allowFullScreen
                  />
                </div>
              </div>
            ) : (
              // 文档列表
              <DocumentList
                documents={filteredDocuments}
                onPreview={handleEmbeddedPreview}
                onDownload={handleDownload}
                onRowPreview={handleRowPreview}
                onSelectionChange={handleSelectionChange}
                onStartTranslation={handleStartTranslation}
                onBatchTranslate={handleBatchTranslate}
                loading={loading}
              />
            )}
          </div>

          {/* 分页 */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            pageSizeOptions={[10, 20, 50, 100]}
          />
        </div>
      </div>

      {/* 文档预览模态框（已移除，使用内嵌预览） */}
      {/* <DocumentPreview
        file={previewFile}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        onDownload={handleDownload}
      /> */}

      {/* OnlyOffice 预览模态框（暂时禁用） */}
      {/* {onlyOfficePreview.isOpen && (
        <OnlyOfficePreview
          rowid={onlyOfficePreview.rowid}
          fileName={onlyOfficePreview.fileName}
          onClose={closeOnlyOfficePreview}
        />
      )} */}

      {/* 批量下载选择模态框 */}
      <BatchDownloadModal
        isOpen={batchDownloadModal.isOpen}
        onClose={closeBatchDownloadModal}
        onConfirm={confirmBatchDownload}
        selectedCount={batchDownloadModal.selectedDocuments.length}
      />

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLoginSuccess={handleLoginSuccess}
        onSwitchToRegister={() => {
          setShowLoginModal(false);
          setShowRegisterModal(true);
        }}
      />

      {/* 注册模态框 */}
      <RegisterModal
        isOpen={showRegisterModal}
        onClose={() => setShowRegisterModal(false)}
        onRegisterSuccess={handleRegisterSuccess}
        onSwitchToLogin={() => {
          setShowRegisterModal(false);
          setShowLoginModal(true);
        }}
      />

      {/* 充值模态框 */}
      <RechargeModal
        isOpen={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
        onRechargeSuccess={handleRechargeSuccess}
      />

      {/* 翻译设置模态框 */}
      <TranslationSettings
        isOpen={showTranslationSettings}
        onClose={handleCloseTranslationSettings}
        onSettingsChange={handleTranslationSettingsChange}
      />

      {/* 批量翻译确认模态框 */}
      <BatchTranslateModal
        isOpen={showBatchTranslateModal}
        onClose={() => setShowBatchTranslateModal(false)}
        onConfirm={confirmBatchTranslate}
        selectedCount={selectedDocuments.size}
      />

    </div>
  );
}

export default App;
